import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

function generateAvailableNodeList(count: number) {
  const dataList: number[] = [];
  for (let i = 0; i < count; i++) {
    const temp = faker.number.int({ min: 1, max: 128 });
    if (dataList.includes(temp)) {
      continue;
    }
    dataList.push(temp);
  }
  return dataList.sort((a, b) => a - b);
}

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  return useResponseSuccess(
    generateAvailableNodeList(faker.number.int({ min: 1, max: 128 })),
  );
});
