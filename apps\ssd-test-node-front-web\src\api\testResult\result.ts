import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace TestResultApi {
  export interface TestResult {
    id: number; // 主键
    nodeId: string; // 哪一个测试节点完成的测试
    diskType: string; // 磁盘类型
    testScript: string; // 测试脚本
    testStartTime: string; // 测试时间
    operator: string; // 操作者
    resultDetail: string; // 自测试项测试详情
    result: boolean; // 测试结果
    resultFilePath?: string; // 测试结果储存路径
  }
}

/**
 * 获取 node status 列表数据
 */
async function getTestResultList(params: Recordable<any>) {
  return requestClient.get<Array<TestResultApi.TestResult>>(
    '/testResult/list',
    {
      params,
    },
  );
}

export { getTestResultList };
