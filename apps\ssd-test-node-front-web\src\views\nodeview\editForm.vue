<script lang="ts" setup>
import type { NodeStatusApi } from '#/api';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { createNodeStatusList, updateNodeStatusList } from '#/api';
import { $t } from '#/locales';

import { useFormSchema } from './dataSchema';

const emits = defineEmits(['success']);

const formData = ref<NodeStatusApi.NodeStatus>();

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const id = ref();
const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    const values = await formApi.getValues();
    if (values.id) {
      values.id = String(values.id);
    }
    console.error(`commit ${values.id} ${id.value}`);
    drawerApi.lock();
    (id.value
      ? updateNodeStatusList(id.value, values)
      : createNodeStatusList(values)
    )
      .then(() => {
        emits('success');
        drawerApi.close();
      })
      .catch(() => {
        drawerApi.unlock();
      });
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<NodeStatusApi.NodeStatus>();
      console.error(`open ${data?.id} ${id?.value}`);
      formApi.resetForm();
      if (data) {
        Form.value = { ...data };
        id.value = data.id;
        switch (data.status) {
          case 0: {
            Form.value.status = $t('page.nodeview.disconnectName');

            break;
          }
          case 1: {
            Form.value.status = $t('page.nodeview.idleName');

            break;
          }
          case 2: {
            Form.value.status = 0;

            break;
          }
          case 3: {
            Form.value.status = 0;
            break;
          }
          // No default
        }
        formApi.setValues(data);
      } else {
        id.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? $t('common.edit', $t('page.nodeview.node'))
    : $t('common.create', $t('page.nodeview.node'));
});
</script>
<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>
<style lang="css" scoped></style>
