<script setup lang="ts">
import type { Ref } from 'vue';

import type { IconType } from '@vben/common-ui';

import { ref } from 'vue';

import { alert, confirm, Page } from '@vben/common-ui';

import { ElCard } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import {
  getAvailableNodeInfoApi,
  getSupportDiskTypeInfoApi,
  getsupportTestScriptInfoApi,
  getTestScriptJsonDataApi,
  getUserInfoApi,
  startTestJobsApi,
} from '#/api';
import { $t } from '#/locales';

const jsonData = ref({ result: '请选择测试脚本' });

const avaTestNodeList = ref(new Array<number>());

// 获取服务器上的脚本信息
async function fetchScriptJsonData(scriptName: string) {
  try {
    const res = await getTestScriptJsonDataApi(scriptName);
    jsonData.value = res;
  } catch (error) {
    jsonData.value = {
      result: `获取服务器脚本${scriptName}失败, 原因: ${error}`,
    };
  }
}

const [newTestJobForm, newTestJobFormApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  commonConfig: {
    // 在label后显示一个冒号
    colon: true,
    // 所有表单项
    componentProps: {
      class: 'flex mx-auto w-1/4',
    },
  },
  // 大屏一行显示1个，中屏一行显示1个，小屏一行显示1个
  // wrapperClass: 'grid-cols-1 md:grid-cols-1 lg:grid-cols-1',
  // 提交函数
  handleSubmit: onSubmit,
  handleReset: async () => {
    await newTestJobFormApi.resetForm();
    const userName = await getUserInfoApi();
    newTestJobFormApi.setFieldValue('user', userName.username);
    jsonData.value = { result: '请选择测试脚本' };
  },
  // 表单如果发生改变 -- 是否需要校验？
  // handleValuesChange(_values, fieldsChanged) {
  // },
  // 垂直布局，label和input在不同行，值为vertical
  // 水平布局，label和input在同一行
  layout: 'horizontal',
  schema: [
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: string[]) => {
          return data.map((item: string) => ({
            label: item,
            value: item,
          }));
        },
        api: getSupportDiskTypeInfoApi,
        // 如果正在获取数据，使用插槽显示一个loading
        showSearch: true,
      },
      // 字段名
      fieldName: 'diskType',
      // 界面显示的label
      label: $t(`page.execute.newTestDiskTypeTitle`),
      rules: 'selectRequired',
    },
    {
      component: 'ApiNodeSelect',
      componentProps: {
        placeholder: $t(`page.execute.newTestNodePlaceholder`),
        autocomplete: false,
        api: getAvailableNodeInfoApi,
        // 菜单接口转options格式
        afterFetch: (data: number[]) => {
          avaTestNodeList.value = data;
          return data.map((item: number) => ({
            label: `${item}`,
            value: item,
          }));
        },
        // 用于支持用户多选
        onChange: (value: Array<number | string>) => {
          const inputValue: number | string | undefined = value.pop();
          if (typeof inputValue === 'string' && inputValue.includes('-')) {
            const [startStr, endStr] = inputValue.split('-');
            const start = Number.parseInt(startStr);
            const end = Number.parseInt(endStr);
            if (!Number.isNaN(start) && !Number.isNaN(end) && start <= end) {
              for (let i = start; i <= end; i++) {
                const tempNodeIndex = i;
                if (
                  avaTestNodeList.value.some(
                    (opt) =>
                      opt === tempNodeIndex && !value.includes(tempNodeIndex),
                  )
                ) {
                  value.push(tempNodeIndex);
                }
              }
            }
          } else if (typeof inputValue === 'number') {
            value.push(inputValue);
          }
          newTestJobFormApi.setFieldValue('targetNodes', value);
        },
      },
      // disabledOnChangeListener: true,
      fieldName: 'targetNodes',
      label: $t(`page.execute.newTestNodeTitle`),
      rules: 'selectRequired',
    },
    {
      // 远程获取可用的测试脚本
      component: 'ApiSelect',
      // 对应组件的参数
      componentProps: {
        // 菜单接口转options格式
        afterFetch: (data: string[]) => {
          return data.map((item: string) => ({
            label: item,
            value: item,
          }));
        },
        api: getsupportTestScriptInfoApi,
        showSearch: true,
        // 当选择的测试脚本改变时，触发依赖
        onChange: fetchScriptJsonData,
      },
      // 字段名
      fieldName: 'testScript',
      // 界面显示的label
      label: $t(`page.execute.newTestTargetScriptTitle`),
      rules: 'selectRequired',
    },
    {
      // 组件需要在 #/adapter.ts内注册，并加上类型
      component: 'JsonDataViewer',
      // 对应组件的参数
      componentProps: {
        value: jsonData,
        copyable: true,
        boxed: true,
        collapsed: false,
        withQuotes: true,
        withBrackets: true,
        expandDepth: 3,
        sort: false,
        class: 'w-full',
      },
      // 字段名
      fieldName: 'jsonViewer',
      // 界面显示的label
      label: $t(`page.execute.newTestTargetScriptPreviewTitle`),
    },
  ],
});

// function onSubmit(values: Record<string, any>) {
//   const errMsg = ref('');
//   confirm({
//     beforeClose({ isConfirm }) {
//       if (isConfirm) {
//         return new Promise((resolve) => {
//           startTestJobsApi({
//             diskType: values.diskType,
//             targetTestNode: values.targetNodes,
//             testScript: values.testScript,
//           }).then((resolve) => {
//             resolve(res);
//             if (res === false) {
//               errMsg.value = '提交失败';
//               resolve(errMsg);
//             } else {
//               errMsg.value = '提交成功';
//               resolve(errMsg);
//             }
//             return true;
//           });
//         });
//       }
//     },
//     content: `本次提交的测试数据:\n ${JSON.stringify(values, null, '\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0')}`,
//     icon: 'warning',
//   }).then(() => {
//     alert(`${errMsg.value}`);
//   });

// function onSubmit(values: Record<string, any>) {
//   const errMsg = ref('');
//   confirm({
//     async beforeClose({ isConfirm }) {
//       if (isConfirm) {
//         return new Promise((resolve) => {
//           const res = await startTestJobsApi({
//             diskType: values.diskType,
//             targetTestNode: values.targetNodes,
//             testScript: values.testScript,
//           }).then((resolve) => {
//             if (res === false) {
//               errMsg.value = '提交失败';
//               resolve(errMsg);
//               console.error(errMsg);
//             } else {
//               errMsg.value = '提交成功';
//               resolve(errMsg);
//               console.error(errMsg);
//             }
//           });
//         });
//       }
//     },
//     content: `本次提交的测试数据:\n ${JSON.stringify(values, null, '\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0')}`,
//     icon: 'warning',
//   }).then(() => {
//     alert(`${errMsg.value}`);
//   });
//   // const errMsg = ref('');
//   // ElMessage.warning(`提交数据：${values}`);
//   // confirm({
//   //   beforeClose({ isConfirm }) {
//   //     if (isConfirm) {
//   //       return new Promise(() =>
//   //         startTestJobsApi({
//   //           diskType: values.diskType,
//   //           targetTestNode: values.targetNodes,
//   //           testScript: values.testScript,
//   //         }).then((res) => {
//   //           errMsg.value = res === false ? 'success' : 'failed';
//   //           return true;
//   //         }),
//   //       );
//   //     }
//   //     return true;
//   //   },
//   //   content: `本次提交的测试数据:\n ${JSON.stringify(values, null, '\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0')}`,
//   //   icon: 'warning',
//   // }).then(() => {
//   //   alert('提交成功');
//   // });
// }
function onSubmit(values: Record<string, any>) {
  // ElMessage.warning(`提交数据：${values}`);
  const errMsg = ref('失败，失败原因：未知错误');
  const resultType: Ref<IconType> = ref('warning');
  confirm({
    async beforeClose({ isConfirm }) {
      if (isConfirm) {
        try {
          const result = await startTestJobsApi({
            diskType: values.diskType,
            targetTestNode: values.targetNodes,
            testScript: values.testScript,
          });
          console.error(result);
          errMsg.value = result.result
            ? '成功'
            : `失败, 失败原因： ${result.msg}`;
          resultType.value = result.result ? 'success' : 'error';
          // 这里可以执行一些异步操作。如果最终返回了false，将阻止关闭弹窗
          return new Promise((resolve) => setTimeout(resolve, 10));
        } catch (error: any) {
          console.error(`catch ${error}`);
          errMsg.value = `失败, 失败原因： ${error.msg}`;
          resultType.value = 'error';
          return new Promise((resolve) => setTimeout(resolve, 10));
        }

        // return false;
      }
    },
    content: `本次提交的测试数据:\n ${JSON.stringify(values, null, '\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0')}`,
    icon: 'warning',
  }).then(() => {
    alert({
      content: `提交${errMsg.value}`,
      icon: resultType.value,
    });
  });
}
</script>
<template>
  <Page
    content-class="flex flex-col gap-4"
    :title="$t(`page.execute.noticeTitle`)"
  >
    <template #description>
      <div class="text-muted-foreground">
        <p>{{ $t(`page.execute.noticeDescription`) }}</p>
      </div>
    </template>
    <ElCard shadow="always">
      <template #header>
        <div class="text-center font-bold">
          <span>{{ $t(`page.execute.newJobTitle`) }}</span>
        </div>
      </template>
      <newTestJobForm />
    </ElCard>
  </Page>
</template>
