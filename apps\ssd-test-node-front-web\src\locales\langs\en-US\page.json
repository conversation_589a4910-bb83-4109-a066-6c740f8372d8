{"auth": {"codeLogin": "Code Login", "forgetPassword": "Forget Password", "login": "<PERSON><PERSON>", "modifyPassword": "ModifyPassword", "newPassword": "New password", "newPasswordAgain": "New password Confirm", "oldPassword": "Old password", "password": "Password", "usernameTip": "Please enter username", "modifyPasswordSubtitle": "Enter your old password and new password, we will change your password", "qrcodeLogin": "Qr Code Login", "verifyCode": "Please enter the verification code received by email", "verifyCodeTip": "Please enter the verification code received by email", "register": "Register", "username": "Username"}, "dashboard": {"analytics": "Analytics", "title": "Dashboard", "workspace": "Workspace"}, "execute": {"newJobTitle": "Create New Test Job", "newTestDiskTypeTitle": "Target Disk Type", "newTestNodePlaceholder": "Please choose the id of target test node, allow multiple choose", "newTestNodeTitle": "Target Node number", "newTestTargetScriptPlaceholder": "Please select the target of test script", "newTestTargetScriptPreviewTitle": "Test Script Preview", "newTestTargetScriptTitle": "Target Test Script", "newTestTimeTitle": "Test Time", "newTestUserTitle": "Target User", "noticeDescription": "The test node can be controlled remotely to execute the corresponding test through this page. At present, this function is in the debugging stage. If there is any problem, please contact the R&D department in time.", "noticeTitle": "TestPage", "title": "TestExecute"}, "nodeview": {"disconnectName": "Disconnect", "waitName": "Done", "id": "ID", "idleName": "Idle", "name": "Name", "node": "Node", "operate": "operation", "progress": "Progress", "raspberryIp": "RaspberryIp", "startTestTime": "StartTestTime", "status": "Status", "stopNo": "No", "stopTitle": "Immediately stop the work", "stopYes": "Yes", "testItem": "CurrentTestItem", "testPcIp": "TestPcIp", "testPcPower": "Test<PERSON>c<PERSON><PERSON>er", "testScript": "TestScript", "title": "NodeView", "user": "User", "workName": "Working"}, "overview": {"listName": "Node List", "noticeDescription": "The current version is a closed test version, the function is not perfect, more functions to be improved!! ! \n If you have any questions, please contact the R&D department in time, thank you.", "noticeTitle": "Notice", "testNodeAvailableDescription": "Number of SSD Test nodes currently available", "testNodeAvailableTitle": "Number of Available", "testNodeDestroyDescription": "The number of SSD Test nodes that have been corrupted and are unusable", "testNodeDestroyTitle": "Number of Damage", "testNodeTotalDescription": "The number of all SSD Test nodes entered into the system", "testNodeTotalTitle": "Number of Total", "testNodeUsedDescription": "Number of SSD Test nodes currently in used", "testNodeUsedTitle": "Number of Used", "title": "Overview"}, "testResultView": {"title": "TestResult", "listName": "ResultList", "id": "Index", "nodeId": "Node ID", "diskType": "DiskType", "testScript": "TestScript", "testStartTime": "StartTime", "operator": "Operator", "resultDetail": "DetailInfo", "result": "TestResult", "resultFilePath": "ResultFilePath"}}