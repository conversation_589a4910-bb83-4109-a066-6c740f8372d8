import { readFile } from 'node:fs/promises';
import { resolve } from 'node:path';
import { fileURLToPath, URL } from 'node:url';

import { verifyAccessToken } from '~/utils/jwt-utils';
import {
  forbiddenResponse,
  unAuthorizedResponse,
  useResponseSuccess,
} from '~/utils/response';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  // 获取请求参数
  const { scriptName } = getQuery(event);
  try {
    // 构建文件路径
    const url = new URL('.', import.meta.url);
    const scriptPath = resolve(
      fileURLToPath(url),
      '../../testScript',
      `${scriptName}`,
    );
    // 读取并解析 JSON 文件
    const fileContent = await readFile(scriptPath, 'utf8');
    const jsonData = JSON.parse(fileContent);

    // 设置响应头
    return useResponseSuccess(jsonData);
  } catch (error) {
    if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
      throw forbiddenResponse(event, 'Script not found');
    }
    return forbiddenResponse(event, 'Invalid JSON format');
  }
});
