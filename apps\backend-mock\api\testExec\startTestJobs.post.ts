import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse } from '~/utils/response';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  const { diskType, targetTestNode, testScript } = await readBody(event);
  console.log(diskType);
  console.log(targetTestNode);
  console.log(testScript);
  return useResponseSuccess({ result: true, msg: '成功' });
});
