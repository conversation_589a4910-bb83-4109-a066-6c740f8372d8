{"name": "@vben/hooks", "version": "5.5.5", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/effects/hooks"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@vben-core/composables": "workspace:*", "@vben/preferences": "workspace:*", "@vben/stores": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "watermark-js-plus": "catalog:"}}