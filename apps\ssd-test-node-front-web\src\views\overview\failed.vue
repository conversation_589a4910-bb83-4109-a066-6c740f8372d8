<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 模拟数据：包含总测试数、失败数（成功数 = 总数 - 失败数）
const testTypeData: Array<{ fail: number; name: string; total: number }> = [
  { name: 'ME3000', total: 32, fail: 5 }, // 成功: 27
  { name: 'ME7000', total: 156, fail: 12 }, // 成功: 144
  { name: 'ME600', total: 28, fail: 3 }, // 成功: 25
  { name: 'MI600', total: 80, fail: 8 }, // 成功: 72
];

onMounted(() => {
  renderEcharts({
    grid: {
      bottom: 0,
      containLabel: true,
      left: '1%',
      right: '1%',
      top: '8%',
    },
    // 新增 legend 配置（图注）
    legend: {
      data: ['失败', '成功'], // 图注名称
      left: 'right', // 图注位置（顶部）
      top: 'top',
      itemWidth: 14, // 颜色块宽度
      itemHeight: 14, // 颜色块高度
      textStyle: {
        fontSize: 12, // 文字大小
      },
    },
    // 堆叠柱状图核心配置：相同 stack 值的系列会堆叠
    series: [
      {
        name: '失败',
        type: 'bar',
        stack: 'testResult', // 关键：相同 stack 名
        data: testTypeData.map((item) =>
          Number(((item.fail / item.total) * 100).toFixed(1)),
        ), // 失败率
        color: '#e84a5f', // 红色
        barMaxWidth: 80,
        label: {
          show: true,
          position: 'inside', // 数值显示在柱体内
          color: 'white',
        },
      },
      {
        name: '成功',
        type: 'bar',
        stack: 'testResult', // 与失败率系列相同
        data: testTypeData.map((item) =>
          Number((((item.total - item.fail) / item.total) * 100).toFixed(1)),
        ), // 成功率
        color: '#62d2a2', // 浅绿色
        barMaxWidth: 80,
        label: {
          show: true,
          position: 'inside', // 数值显示在柱体内
          color: 'white',
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      // 自定义提示框内容，显示失败/成功具体数值和比例
      formatter: (
        params: Array<{ fail: number; name: string; total: number }>,
      ) => {
        const item = testTypeData.find((i) => i.name === params[0].name)!;
        const failRate = ((item.fail / item.total) * 100).toFixed(1);
        const successRate = (100 - Number(failRate)).toFixed(1);
        return `
          ${item.name}<br/>
          失败：${item.fail} (${failRate}%)<br/>
          成功：${item.total - item.fail} (${successRate}%)
        `;
      },
    },
    xAxis: {
      data: testTypeData.map((item) => item.name),
      type: 'category',
    },
    yAxis: {
      max: 100, // 百分比最大为100%
      splitNumber: 5, // 分割为5段（0%, 20%, 40%, ..., 100%）
      type: 'value',
      axisLabel: {
        formatter: '{value}%', // Y轴标签显示百分比
      },
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" class="h-64" />
  <!-- 设置图表高度 -->
</template>
