import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    accessToken: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }

  export interface EmailInfo {
    email: string;
  }

  export interface ResetPasswordInfo {
    username: string;
    newPassword: string;
    verifyCode: string;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>('/auth/login', data);
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return requestClient.post('/auth/logout', {
    withCredentials: true,
  });
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/auth/codes');
}

/**
 * 发送验证码
 */
export async function postEmailCodeApi(data: AuthApi.EmailInfo) {
  return baseRequestClient.post<boolean>('/auth/forget-password', data);
}

/**
 * 重置用户密码
 */
export async function postRestPasswordApi(data: AuthApi.ResetPasswordInfo) {
  return baseRequestClient.post<boolean>('/auth/reset-password', data);
}
