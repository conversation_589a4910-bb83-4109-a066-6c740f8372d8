{"auth": {"codeLogin": "验证码登录", "forgetPassword": "忘记密码", "login": "登录", "modifyPassword": "修改密码", "modifyPasswordSubtitle": "输入您的您的旧密码和新密码，我们将为您修改密码", "newPassword": "请输入新密码", "newPasswordAgain": "再次输入新密码", "oldPassword": "旧密码", "password": "密码", "qrcodeLogin": "二维码登录", "register": "注册", "username": "用户名", "usernameTip": "请输入用户名", "verifyCode": "请输入邮箱接收到的验证码", "verifyCodeTip": "请输入邮箱接收到的6位数字的验证码"}, "dashboard": {"analytics": "分析页", "title": "概览", "workspace": "工作台"}, "execute": {"newJobTitle": "创建新的测试任务", "newTestDiskTypeTitle": "目标磁盘类型", "newTestNodePlaceholder": "请选择目标测试节点的ID,允许通过‘start-end’选择多个", "newTestNodeTitle": "目标节点编号", "newTestTargetScriptPreviewTitle": "测试脚本预览", "newTestTargetScriptTitle": "目标测试脚本", "newTestTimeTitle": "测试时间", "newTestUserTitle": "使用者", "noticeDescription": "可通过该页面远程控制测试节点执行相应的测试。目前处于该功能处于调试阶段，如有问题，请及时联系研发部。", "noticeTitle": "测试页面", "title": "测试执行"}, "nodeview": {"disconnectName": "未连接", "id": "编号", "idleName": "就绪", "listName": "节点列表", "name": "节点名称", "node": "节点", "operate": "操作", "progress": "测试进度", "raspberryIp": "树莓派IP", "startTestTime": "开始测试时间", "status": "节点状态", "stopNo": "否", "stopTitle": "是否立即停止工作", "stopYes": "是", "testItem": "当前测试项", "testPcIp": "测试电脑IP", "testPcPower": "测试电脑电源状态", "testScript": "测试脚本", "title": "节点详情", "user": "当前使用者", "waitName": "测试完成", "workName": "工作中"}, "overview": {"noticeDescription": "当前版本为内测版本, 功能不够完善, 请优先使用'中文'以及'亮色'版本, 其他功能版本待后续完善！！!\n如果发现任何疑问, 请及时联系研发部, 谢谢.", "noticeTitle": "公告", "testNodeAvailableDescription": "当前SSD Test Node可用使用的数量", "testNodeAvailableTitle": "可用数量", "testNodeDestroyDescription": "SSD Test Node已经损坏并无法使用的数量", "testNodeDestroyTitle": "损坏数量", "testNodeTotalDescription": "所有录入到系统中的SSD Test Node数量", "testNodeTotalTitle": "总数量", "testNodeUsedDescription": "当前SSD Test Node正在被使用的数量", "testNodeUsedTitle": "已用数量", "title": "总览"}, "testResultView": {"diskType": "磁盘类型", "id": "序号", "listName": "结果列表", "nodeId": "测试Node", "operator": "操作者", "result": "测试结果", "resultDetail": "子测试项结果详情", "resultFilePath": "测试结果路径", "testScript": "测试脚本", "testStartTime": "开始时间", "title": "测试结果"}}