<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import type { AuthApi } from '#/api';

import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationForgetPassword, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { ElMessage } from 'element-plus';

import { postEmailCodeApi, postRestPasswordApi } from '#/api';

defineOptions({ name: 'ForgetPassword' });

const loading = ref(false);
const router = useRouter();
const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('page.auth.username'),
      },
      fieldName: 'username',
      label: $t('page.auth.username'),
      rules: z.string().min(1, { message: $t('page.auth.usernameTip') }),
    },
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('page.auth.verifyCode'),
      },
      fieldName: 'verifyCode',
      label: $t('page.auth.verifyCode'),
      rules: z
        .string()
        .min(6, { message: $t('page.auth.verifyCodeTip') })
        .max(6, { message: $t('page.auth.verifyCodeTip') })
        .refine((value) => /^\d{6}$/.test(value), {
          message: $t('page.auth.verifyCodeTip'),
        }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: $t('page.auth.newPassword'),
      },
      fieldName: 'newPassword',
      label: $t('page.auth.newPassword'),
      renderComponentContent() {
        return {
          strengthText: () => $t('authentication.passwordStrength'),
        };
      },
      rules: z.string().min(1, { message: $t('page.auth.newPassword') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('page.auth.newPasswordAgain'),
      },
      dependencies: {
        rules(values) {
          const { newPassword } = values;
          return z
            .string({ required_error: $t('authentication.passwordTip') })
            .min(1, { message: $t('authentication.passwordTip') })
            .refine((value) => value === newPassword, {
              message: $t('authentication.confirmPasswordTip'),
            });
        },
        triggerFields: ['newPassword'],
      },
      fieldName: 'confirmPassword',
      label: $t('page.auth.newPasswordAgain'),
    },
  ];
});

async function handleSubmit(value: Recordable<any>) {
  const { username, verifyCode, newPassword } = value;
  const data: AuthApi.ResetPassword = {
    username,
    newPassword,
    verifyCode,
  };
  await postRestPasswordApi(data)
    .then((res) => {
      if (res.data.data === true) {
        ElMessage.success('密码重置成功，请重新登录');
        router.push('/auth/login');
      } else {
        ElMessage.error(`${res.data.msg}`);
      }
    })
    .catch((error) => {
      ElMessage.error(`${error.msg}`);
    });
}

const disableSendEmail = ref(false);

async function handleEmailSubmit(
  value: Recordable<any>,
  callback: (flag: boolean, text?: string) => void,
) {
  callback(true, '正在发送...');
  const emailRegex: RegExp = /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/;
  if (emailRegex.test(value)) {
    const data: AuthApi.EmailInfo = {
      email: value,
    };
    await postEmailCodeApi(data)
      .then((res) => {
        if (res.data.data === true) {
          ElMessage.success('验证码发送成功，请注意查收');
          callback(false, '');
        } else {
          ElMessage.error(`${res.data.msg}`);
          callback(false, '');
        }
      })
      .catch((error) => {
        ElMessage.error(`${error.msg}`);
        callback(false, '');
      });
  } else {
    ElMessage.error('非法邮箱, 请重新输入');
    callback(false, '');
  }
}
</script>

<template>
  <AuthenticationForgetPassword
    :form-schema="formSchema"
    :loading="loading"
    :disabled="disableSendEmail"
    @submit="handleSubmit"
    @submit-email="handleEmailSubmit"
  />
</template>
