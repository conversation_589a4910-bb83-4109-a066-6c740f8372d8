<script lang="ts" setup>
import type {
  TestNodeManagerViewItem,
  TestNodeSuportItem,
} from '@vben/common-ui';
import type { TabOption } from '@vben/types';

import {
  ChartsOverview,
  OverviewNotice,
  SystemSupportOverview,
  TestNodeOverview,
} from '@vben/common-ui';
import {
  SvgAvailableIcon,
  SvgCardIcon,
  SvgErrorIcon,
  SvgWaringIcon,
} from '@vben/icons';

import { $t } from '#/locales';

import OverviewFailedRatio from './failed.vue';
import OverviewTestType from './test-type.vue';
import OverviewVisits from './visits.vue';

const overviewItems: TestNodeManagerViewItem[] = [
  {
    icon: SvgAvailableIcon,
    title: $t('page.overview.testNodeAvailableTitle'),
    value: 128,
    description: $t('page.overview.testNodeAvailableDescription'),
  },
  {
    icon: SvgWaringIcon,
    title: $t('page.overview.testNodeUsedTitle'),
    value: 0,
    description: $t('page.overview.testNodeUsedDescription'),
  },
  {
    icon: SvgErrorIcon,
    title: $t('page.overview.testNodeDestroyTitle'),
    value: 0,
    description: $t('page.overview.testNodeDestroyDescription'),
  },
  {
    icon: SvgCardIcon,
    title: $t('page.overview.testNodeTotalTitle'),
    value: 128,
    description: $t('page.overview.testNodeTotalDescription'),
  },
];

const systemSupportItems: TestNodeSuportItem[] = [
  {
    title: '支持测试的盘类型',
    detail: ['M.2 消费级SSD', 'SATA 消费级SSD', 'U.2 企业级SSD'],
  },
  {
    title: '支持测试系统',
    detail: [
      'Ubuntu20.04',
      'Ubuntu22.04',
      'Ubuntu24.04',
      'Windows11',
      'Windows10',
    ],
  },
  {
    title: '支持测试软件',
    detail: ['FIO', 'BIT'],
  },
];

const chartTabs: TabOption[] = [
  {
    label: '月访问量',
    value: 'visits',
  },
  {
    label: '各类型盘测试量',
    value: 'testType',
  },
  {
    label: '各类型测试失败率',
    value: 'failed',
  },
];
</script>

<template>
  <div class="mt-5 flex flex-col gap-6 p-5">
    <OverviewNotice
      :title="$t('page.overview.noticeTitle')"
      :detail="$t('page.overview.noticeDescription')"
    />
    <TestNodeOverview :items="overviewItems" />
    <SystemSupportOverview :items="systemSupportItems" />
    <ChartsOverview :tabs="chartTabs" class="mt-5">
      <template #visits>
        <OverviewVisits />
      </template>
      <template #testType>
        <OverviewTestType />
      </template>
      <template #failed>
        <OverviewFailedRatio />
      </template>
    </ChartsOverview>
  </div>
</template>
