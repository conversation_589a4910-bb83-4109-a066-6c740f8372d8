<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { NodeStatusApi } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { ElButton, ElMessage } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteNodeStatusList, getNodeStatusList } from '#/api';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './dataSchema';
import Form from './editForm.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

function onActionClick(e: OnActionClickParams<NodeStatusApi.NodeStatus>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

function onDelete(row: NodeStatusApi.NodeStatus) {
  const hideLoading = ElMessage({
    message: $t('ui.actionMessage.deleting', [row.name]),
    showClose: true,
  });
  deleteNodeStatusList(row.id)
    .then(() => {
      hideLoading.close();
      ElMessage({
        message: $t('ui.actionMessage.deleteSuccess', [row.name]),
        type: 'success',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading.close();
      hideLoading();
    });
}

function onEdit(row: NodeStatusApi.NodeStatus) {
  formDrawerApi.setData(row).open();
}

function onRefresh() {
  gridApi.query();
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getNodeStatusList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },

    toolbarConfig: {
      custom: true,
      export: false,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<NodeStatusApi.NodeStatus>,
});

function onCreate() {
  formDrawerApi.setData({}).open();
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid :table-title="$t('page.nodeview.listName')">
      <template #toolbar-tools>
        <ElButton type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('page.nodeview.node')]) }}
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
