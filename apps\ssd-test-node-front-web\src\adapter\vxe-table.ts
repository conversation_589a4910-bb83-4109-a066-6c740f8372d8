import type { Recordable } from '@vben/types';

import { h } from 'vue';

import { $te } from '@vben/locales';
import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { get, isFunction, isString } from '@vben/utils';

import {
  ElButton,
  ElImage,
  ElPopconfirm,
  ElProgress,
  ElSwitch,
  ElTag,
} from 'element-plus';

import { $t } from '#/locales';

import { useVbenForm } from './form';

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.setConfig({
      grid: {
        align: 'center',
        border: false,
        columnConfig: {
          resizable: true,
        },
        minHeight: 180,
        formConfig: {
          // 全局禁用vxe-table的表单配置，使用formOptions
          enabled: false,
        },
        proxyConfig: {
          autoLoad: true,
          response: {
            result: 'items',
            total: 'total',
            list: 'items',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        round: true,
        showOverflow: true,
        size: 'small',
        scrollY: {
          enabled: true,
          gt: 0,
          mode: 'wheel',
        },
      },
    });
    /**
     * 解决vxeTable在热更新时可能会出错的问题
     */
    vxeUI.renderer.forEach((_item, key) => {
      if (key.startsWith('Cell')) {
        vxeUI.renderer.delete(key);
      }
    });

    // 表格配置项可以用 cellRender: { name: 'CellImage' },
    vxeUI.renderer.add('CellImage', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        const src = row[column.field];
        return h(ElImage, { src, previewSrcList: [src] });
      },
    });

    // 表格配置项可以用 cellRender: { name: 'CellLink' },
    vxeUI.renderer.add('CellLink', {
      renderTableDefault(renderOpts) {
        const { props } = renderOpts;
        return h(
          ElButton,
          { size: 'small', link: true },
          { default: () => props?.text },
        );
      },
    });

    // 这里可以自行扩展 vxe-table 的全局配置，比如自定义格式化
    // 单元格渲染： Tag
    vxeUI.renderer.add('CellTag', {
      renderTableDefault({ props }, { column, row }) {
        const value = get(row, column.field);
        const getTagType = (value: any | boolean) => {
          return value === true ? 'success' : 'danger';
        };
        const getTagLabel = (value: any | boolean) => {
          return value === true
            ? $t('global.result.success')
            : $t('global.result.failed');
        };
        return h(
          ElTag,
          {
            ...props,
            effect: 'dark',
            type: getTagType(value),
          },
          { default: () => getTagLabel(value) },
        );
      },
    });

    vxeUI.renderer.add('CellStatus', {
      renderTableDefault({ props }, { column, row }) {
        const value = get(row, column.field);
        const getTagType = (value: any | number) => {
          switch (value) {
            case 0: {
              return 'info';
            }
            case 1: {
              return 'success';
            }
            case 2: {
              return 'warning';
            }
            default: {
              return 'danger';
            }
          }
        };
        const getTagLabel = (value: any | number) => {
          switch (value) {
            case 0: {
              return $t('page.nodeview.disconnectName');
            }
            case 1: {
              return $t('page.nodeview.idleName');
            }
            case 2: {
              return $t('page.nodeview.workName');
            }
            default: {
              return $t('page.nodeview.waitName');
            }
          }
        };
        return h(
          ElTag,
          {
            ...props,
            effect: 'dark',
            type: getTagType(value),
          },
          { default: () => getTagLabel(value) },
        );
      },
    });

    vxeUI.renderer.add('CellProgress', {
      renderTableDefault({ props }, { column, row }) {
        const value = get(row, column.field);
        return h(ElProgress, {
          ...props,
          textInside: true,
          percentage: Number(value),
          strokeWidth: 24,
          color: '#6d9df8',
        });
      },
    });

    vxeUI.renderer.add('CellSwitch', {
      renderTableDefault({ props }, { column, row }) {
        const value = get(row, column.field);
        return h(
          ElSwitch,
          {
            activeText: $t('global.power.on'),
            activeValue: 1,
            inactiveValue: 0,
            inactiveText: $t('global.power.off'),
            ...props,
            modelValue: value,
            inlinePrompt: true,
            disabled: true,
            size: 'large',
          },
          { default: () => value },
        );
      },
    });

    vxeUI.renderer.add('CellOperation', {
      renderTableDefault({ attrs, options, props }, { column, row }) {
        const defaultProps = { size: 'small', ...props };
        let align = 'center';
        switch (column.align) {
          case 'center': {
            align = 'center';
            break;
          }
          case 'left': {
            align = 'start';
            break;
          }
          default: {
            align = 'end';
            break;
          }
        }
        const presets: Recordable<Recordable<any>> = {
          delete: {
            danger: true,
            text: true,
            type: 'danger',
          },
          edit: {
            text: true,
            type: 'primary',
          },
        };
        const operations: Array<Recordable<any>> = (
          options || ['edit', 'delete']
        )
          .map((opt) => {
            if (isString(opt)) {
              return presets[opt]
                ? {
                    code: opt,
                    textInfo: $te(`common.${opt}`) ? $t(`common.${opt}`) : opt,
                    ...presets[opt],
                    ...defaultProps,
                  }
                : {
                    code: opt,
                    textInfo: $te(`common.${opt}`) ? $t(`common.${opt}`) : opt,
                    ...defaultProps,
                  };
            } else {
              return { ...defaultProps, ...presets[opt.code], ...opt };
            }
          })
          .map((opt) => {
            const optBtn: Recordable<any> = {};
            Object.keys(opt).forEach((key) => {
              optBtn[key] = isFunction(opt[key]) ? opt[key](row) : opt[key];
            });
            return optBtn;
          })
          .filter((opt) => opt.show !== false);

        function renderBtn(opt: Recordable<any>, listen = true) {
          return h(
            ElButton,
            {
              ...props,
              ...opt,
              icon: undefined,
              onClick: listen
                ? () =>
                    attrs?.onClick?.({
                      code: opt.code,
                      row,
                    })
                : undefined,
            },
            {
              default: () => {
                return opt.textInfo;
                // const content = [];
                // if (opt.icon) {
                //   content.push(
                //     h(IconifyIcon, { class: 'size-5', icon: opt.icon }),
                //   );
                // }
                // content.push(opt.text);
                // return content;
              },
            },
          );
        }

        function renderConfirm(opt: Recordable<any>) {
          return h(
            ElPopconfirm,
            {
              title: $t('ui.actionTitle.delete', [attrs?.nameTitle || '']),
              confirmButtonText: $t('common.confirm'),
              cancelButtonText: $t('common.cancel'),
              ...props,
              ...opt,
              onConfirm: () => {
                attrs?.onClick?.({
                  code: opt.code,
                  row,
                });
              },
            },
            {
              reference: () => renderBtn({ ...opt }, false),
            },
          );
        }

        const btns = operations.map((opt) =>
          opt.code === 'delete' ? renderConfirm(opt) : renderBtn(opt),
        );
        return h(
          'div',
          {
            class: 'flex table-operations',
            style: { justifyContent: align },
          },
          btns,
        );
      },
    });
  },
  useVbenForm,
});

export { useVbenVxeGrid };

export type OnActionClickParams<T = Recordable<any>> = {
  code: string;
  row: T;
};

export type OnActionClickFn<T = Recordable<any>> = (
  params: OnActionClickParams<T>,
) => void;

export type * from '@vben/plugins/vxe-table';
