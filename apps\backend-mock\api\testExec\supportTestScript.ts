import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

const supportTestScript = [
  '企业级测试脚本.json',
  '消费级测试脚本.json',
  '工业级测试脚本.json',
  'FIO测试脚本.json',
  'BIT测试脚本.json',
];

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  return useResponseSuccess(supportTestScript);
});
