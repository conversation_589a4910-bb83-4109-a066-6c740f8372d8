{"openapi": "3.1.0", "info": {"title": "ssd test node backend", "description": "ssd test node backend接口文档", "version": "0.1.0"}, "paths": {"/api/user/info": {"get": {"tags": ["用户模块"], "summary": "Getuserinfo", "operationId": "getUserInfo_api_user_info_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/auth/login": {"post": {"tags": ["登录模块"], "summary": "<PERSON><PERSON>", "operationId": "login_api_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfo"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Token"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/auth/codes": {"get": {"tags": ["登录模块"], "summary": "Getauthcodes", "operationId": "getAuthCodes_api_auth_codes_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/auth/logout": {"post": {"tags": ["登录模块"], "summary": "Logout", "description": "不能校验token, 因为token可能已经过期了\nTODO : 删除redis中的token ??", "operationId": "logout_api_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/auth/forget-password": {"post": {"tags": ["登录模块"], "summary": "Forgetpassword", "operationId": "forgetPassword_api_auth_forget_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailInfo"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/auth/reset-password": {"post": {"tags": ["登录模块"], "summary": "Resetpassword", "operationId": "resetPassword_api_auth_reset_password_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordInfo"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/auth/register": {"post": {"tags": ["登录模块"], "summary": "Createaccount", "operationId": "createAccount_api_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserInfo"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/node/status": {"get": {"tags": ["节点管理"], "summary": "Getnodestatus", "operationId": "getNodeStatus_api_node_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "post": {"tags": ["节点管理"], "summary": "Addnodestatus", "description": "添加测试节点状态信息\n:param request: Request对象\n:param user: 当前用户\n:param queryDB: orm对象\n:return:", "operationId": "addNodeStatus_api_node_status_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestNodeBaseInfo"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/node/status/{id}": {"put": {"tags": ["节点管理"], "summary": "Updatenodestatus", "operationId": "updateNodeStatus_api_node_status__id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "title": "Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TestNodeBaseInfo"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["节点管理"], "summary": "Deletenodestatus", "description": "删除测试节点状态信息\n:param request: Request对象\n:param user: 当前用户\n:param queryDB: orm对象\n:return:", "operationId": "deleteNodeStatus_api_node_status__id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "title": "Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/testExec/availableNode": {"get": {"tags": ["测试执行"], "summary": "Getnodestatus", "operationId": "getNodeStatus_api_testExec_availableNode_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/testExec/supportDiskType": {"get": {"tags": ["测试执行"], "summary": "Getsupportdisktype", "operationId": "getSupportDiskType_api_testExec_supportDiskType_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/testExec/supportTestScript": {"get": {"tags": ["测试执行"], "summary": "Getsupporttestscript", "operationId": "getSupportTestScript_api_testExec_supportTestScript_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/testExec/testScriptJsonData": {"get": {"tags": ["测试执行"], "summary": "Gettestscriptjsondata", "operationId": "getTestScriptJsonData_api_testExec_testScriptJsonData_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/testExec/startTestJobs": {"post": {"tags": ["测试执行"], "summary": "Gettestscriptjsondata", "operationId": "getTestScriptJsonData_api_testExec_startTestJobs_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StartTestJobInfo"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/testResult/list": {"get": {"tags": ["测试结果"], "summary": "Gettestresultlist", "operationId": "getTestResultList_api_testResult_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/testLog/rootFolders": {"get": {"tags": ["测试日志"], "summary": "Gettestlogrootfolders", "description": "获取测试日志根目录下的文件夹列表", "operationId": "getTestLogRootFolders_api_testLog_rootFolders_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/testLog/files": {"get": {"tags": ["测试日志"], "summary": "Gettestlogfilelist", "description": "获取指定目录下的文件列表", "operationId": "getTestLogFileList_api_testLog_files_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": true, "schema": {"type": "string", "description": "目录路径", "title": "<PERSON><PERSON><PERSON>"}, "description": "目录路径"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/testLog/fileContent": {"get": {"tags": ["测试日志"], "summary": "Gettestlogfilecontent", "description": "获取指定文件内容", "operationId": "getTestLogFileContent_api_testLog_fileContent_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "filePath", "in": "query", "required": true, "schema": {"type": "string", "description": "文件路径", "title": "Filepath"}, "description": "文件路径"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/testLog/parent": {"get": {"tags": ["测试日志"], "summary": "Gettestlogparentdirectory", "description": "获取父级目录内容", "operationId": "getTestLogParentDirectory_api_testLog_parent_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "currentPath", "in": "query", "required": true, "schema": {"type": "string", "description": "当前路径", "title": "Currentpath"}, "description": "当前路径"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"EmailInfo": {"properties": {"email": {"type": "string", "title": "Email"}}, "type": "object", "required": ["email"], "title": "EmailInfo"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "NewUserInfo": {"properties": {"username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "email": {"type": "string", "title": "Email"}, "verifyCode": {"type": "string", "title": "Verifycode"}}, "type": "object", "required": ["username", "password", "email", "verifyCode"], "title": "NewUserInfo"}, "ResetPasswordInfo": {"properties": {"username": {"type": "string", "title": "Username"}, "newPassword": {"type": "string", "title": "Newpassword"}, "verifyCode": {"type": "string", "title": "Verifycode"}}, "type": "object", "required": ["username", "newPassword", "verifyCode"], "title": "ResetPasswordInfo"}, "StartTestJobInfo": {"properties": {"diskType": {"type": "string", "title": "Disktype", "description": "磁盘名称"}, "targetTestNode": {"items": {"type": "integer"}, "type": "array", "title": "Targettestnode", "description": "目标测试节点"}, "testScript": {"type": "string", "title": "Testscript", "description": "测试脚本"}}, "type": "object", "required": ["diskType", "targetTestNode"], "title": "StartTestJobInfo"}, "TestNodeBaseInfo": {"properties": {"id": {"type": "string", "title": "Id", "description": "节点编号"}, "name": {"type": "string", "title": "Name", "description": "节点名称"}, "raspberryIp": {"type": "string", "title": "Raspberryip", "description": "节点树莓派IP"}, "testPcIp": {"type": "string", "title": "Testpcip", "description": "节点测试PC IP"}, "status": {"type": "integer", "title": "Status", "description": "节点状态，0-未测试，1-测试中，2-测试完成", "default": 0}}, "type": "object", "required": ["id", "name"], "title": "TestNodeBaseInfo"}, "Token": {"properties": {"accessToken": {"type": "string", "title": "Accesstoken", "description": "token信息"}, "tokenType": {"type": "string", "title": "Tokentype", "description": "token类型"}}, "type": "object", "required": ["accessToken", "tokenType"], "title": "Token"}, "UserInfo": {"properties": {"username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "loginLog": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Loginlog"}}, "type": "object", "required": ["username", "password"], "title": "UserInfo"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/user/info"}}}}}}