import { requestClient } from '#/api/request';

/** 测试接口参数 */
export interface TestJobsParams {
  diskType: string;
  targetTestNode: number[];
  testScript: string;
}

interface JobStatus {
  result: boolean;
  msg: string;
}

/**
 * 获取用户信息
 */
export async function startTestJobsApi(data: TestJobsParams) {
  const info = requestClient.post<JobStatus>('/testExec/startTestJobs', data);
  console.error(info);
  return info;
}
