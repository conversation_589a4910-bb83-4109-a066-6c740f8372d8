export default defineEventHandler(() => {
  return `
<h1>Hello Vben Admin</h1>
<h2>Mock service is starting</h2>
<ul>
<li><a href="/api/user">/api/user/info</a></li>
<li><a href="/api/menu">/api/menu/all</a></li>
<li><a href="/api/auth/codes">/api/auth/codes</a></li>
<li><a href="/api/auth/login">/api/auth/login</a></li>
<li><a href="/api/upload">/api/upload</a></li>
<li><a href="/api/node/status">/api/node/status</a></li>
<li><a href="/api/testExec/supportDiskType">/api/testExec/supportDiskType</a></li>
<li><a href="/api/testExec/availableNode">/api/testExec/availableNode</a></li>
<li><a href="/api/testExec/supportTestScript">/api/testExec/supportTestScript</a></li>
<li><a href="/api/testExec/testScriptJsonData">/api/testExec/testScriptJsonData</a></li>
<li><a href="/api/testExec/startTestJobs">/api/testExec/startTestJobs</a></li>
</ul>
`;
});
