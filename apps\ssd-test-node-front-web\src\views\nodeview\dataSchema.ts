import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { NodeStatusApi } from '#/api';

import { $t } from '#/locales';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'id',
      label: $t('page.nodeview.id'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: $t('page.nodeview.name'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'raspberryIp',
      label: $t('page.nodeview.raspberryIp'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'testPcIp',
      label: $t('page.nodeview.testPcIp'),
      rules: 'required',
    },
    // {
    //   component: 'RadioGroup',
    //   componentProps: {
    //     allowClear: true,
    //     options: [
    //       { label: $t('global.power.off'), value: 0 },
    //       { label: $t('global.power.on'), value: 1 },
    //     ],
    //   },
    //   fieldName: 'testPcPower',
    //   label: $t('page.nodeview.testPcPower'),
    // },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('page.nodeview.disconnectName'), value: 0 },
          { label: $t('page.nodeview.idleName'), value: 1 },
        ],
      },
      fieldName: 'status',
      label: $t('page.nodeview.status'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'id',
      label: $t('page.nodeview.id'),
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('page.nodeview.disconnectName'), value: 0 },
          { label: $t('page.nodeview.idleName'), value: 1 },
          { label: $t('page.nodeview.workName'), value: 2 },
          { label: $t('page.nodeview.waitName'), value: 3 },
        ],
      },
      fieldName: 'status',
      label: $t('page.nodeview.status'),
    },
    {
      component: 'Input',
      fieldName: 'raspberryIp',
      label: $t('page.nodeview.raspberryIp'),
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'testPcIp',
      label: $t('page.nodeview.testPcIp'),
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'user',
      label: $t('page.nodeview.user'),
      componentProps: {
        clearable: true,
      },
    },
  ];
}

export function useColumns<T = NodeStatusApi.NodeStatus>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('page.nodeview.id'),
      width: 50,
      maxWidth: 100,
    },
    {
      field: 'name',
      title: $t('page.nodeview.name'),
      width: 100,
    },
    {
      field: 'raspberryIp',
      title: $t('page.nodeview.raspberryIp'),
      width: 150,
    },
    {
      field: 'testPcIp',
      title: $t('page.nodeview.testPcIp'),
      width: 150,
    },
    {
      cellRender: {
        name: 'CellSwitch',
      },
      field: 'testPcPower',
      title: $t('page.nodeview.testPcPower'),
      width: 150,
    },
    {
      cellRender: {
        name: 'CellStatus',
      },
      field: 'status',
      title: $t('page.nodeview.status'),
      width: 100,
    },
    {
      field: 'testScript',
      title: $t('page.nodeview.testScript'),
      width: 200,
    },
    {
      field: 'testItemName',
      title: $t('page.nodeview.testItem'),
      width: 200,
    },
    {
      cellRender: {
        name: 'CellProgress',
      },
      field: 'progress',
      title: $t('page.nodeview.progress'),
      // width: 200,
    },
    {
      field: 'startTestTime',
      title: $t('page.nodeview.startTestTime'),
      width: 200,
    },
    {
      field: 'user',
      title: $t('page.nodeview.user'),
      width: 200,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          nameTitle: $t('page.nodeview.node'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('page.nodeview.operate'),
      width: 130,
    },
  ];
}
