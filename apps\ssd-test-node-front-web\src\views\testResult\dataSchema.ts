import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { $t } from '#/locales';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'nodeId',
      label: $t('page.testResultView.nodeId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'diskType',
      label: $t('page.testResultView.diskType'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'operator',
      label: $t('page.testResultView.operator'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'result',
      label: $t('page.testResultView.result'),
      rules: 'required',
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'nodeId',
      label: $t('page.testResultView.nodeId'),
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'diskType',
      label: $t('page.testResultView.diskType'),
      componentProps: {
        clearable: true,
      },
    },
    {
      component: 'Input',
      fieldName: 'operator',
      label: $t('page.testResultView.operator'),
      componentProps: {
        clearable: true,
      },
    },
    {
      fieldName: 'result',
      label: $t('page.testResultView.result'),
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('global.result.failed'), value: 0 },
          { label: $t('global.result.success'), value: 1 },
        ],
      },
    },
  ];
}

export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('page.testResultView.id'),
      width: 50,
      maxWidth: 100,
    },
    {
      field: 'nodeId',
      title: $t('page.testResultView.nodeId'),
      width: 100,
      maxWidth: 100,
    },
    {
      field: 'diskType',
      title: $t('page.testResultView.diskType'),
      width: 100,
    },
    {
      field: 'testScript',
      title: $t('page.testResultView.testScript'),
      width: 150,
    },
    {
      field: 'testStartTime',
      title: $t('page.testResultView.testStartTime'),
      width: 200,
    },
    {
      field: 'operator',
      title: $t('page.testResultView.operator'),
      width: 150,
    },
    {
      field: 'resultDetail',
      title: $t('page.testResultView.resultDetail'),
    },
    {
      cellRender: {
        name: 'CellTag',
      },
      field: 'result',
      title: $t('page.testResultView.result'),
      width: 150,
    },
    {
      field: 'resultFilePath',
      title: $t('page.testResultView.resultFilePath'),
      width: 150,
    },
  ];
}
