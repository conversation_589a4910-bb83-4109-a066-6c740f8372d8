import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace NodeStatusApi {
  export interface NodeStatus {
    id: string; // 节点ID
    name: string; // 节点名称

    /** status: 节点状态
     * 0: 未连接
     * 1: idle
     * 2: working
     * 3: wait for get result
     */
    raspberryIp: string; // 树莓派IP
    testPcIp: string; // 测试PC IP
    testPcPower: 0 | 1; // 测试PC电源状态
    status: number; // 节点状态
    testScript?: string; // 测试脚本
    testItemName?: string; // 当前的测试项
    startTestTime?: string; // 开始测试时间
    user?: string; // 节点使用者
    progress?: number; // 总进度, 0 - 100
  }
}

/**
 * 获取 node status 列表数据
 */
async function getNodeStatusList(params: Recordable<any>) {
  return requestClient.get<Array<NodeStatusApi.NodeStatus>>('/node/status', {
    params,
  });
}

/**
 * 创建node status 数据
 * @param data 角色数据
 */
async function createNodeStatusList(
  data: Omit<NodeStatusApi.NodeStatus, 'id'>,
) {
  console.error(`post create ${data}`);
  return requestClient.post('/node/status', data);
}

/**
 * 更新node status 数据
 * @param id 角色ID
 * @param data 角色数据
 */
async function updateNodeStatusList(
  id: string,
  data: Omit<NodeStatusApi.NodeStatus, 'id'>,
) {
  console.error(`post update ${data}`);
  return requestClient.put(`/node/status/${id}`, data);
}

/**
 * 删除node status 数据
 * @param id 编号id
 */
async function deleteNodeStatusList(id: string) {
  return requestClient.delete(`/node/status/${id}`);
}

export {
  createNodeStatusList,
  deleteNodeStatusList,
  getNodeStatusList,
  updateNodeStatusList,
};
