<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import { onMounted, ref } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

const testTypeData = [
  {
    name: 'ME3000',
    num: 32,
  },
  {
    name: 'ME7000',
    num: 156,
  },
  {
    name: 'ME600',
    num: 28,
  },
  {
    name: 'MI600',
    num: 80,
  },
];

onMounted(() => {
  renderEcharts({
    grid: {
      bottom: 0,
      containLabel: true,
      left: '1%',
      right: '1%',
      top: '8 %',
    },
    series: [
      {
        barMaxWidth: 80,
        data: testTypeData.map((item) => item.num),
        type: 'bar',
        color: '#1BA1fd',
        label: {
          show: true,
          position: 'top',
        },
      },
    ],
    tooltip: {
      axisPointer: {
        lineStyle: {
          width: 1,
        },
      },
      trigger: 'axis',
    },
    xAxis: {
      data: testTypeData.map((item) => item.name),
      type: 'category',
    },
    yAxis: {
      max: 'auto',
      splitNumber: 4,
      type: 'value',
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" />
</template>
