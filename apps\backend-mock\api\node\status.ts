import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem: Record<string, any> = {
      id: i + 1,
      name: `测试节点${i + 1}` as string,
      status: faker.helpers.arrayElement([0, 1, 2, 3]),
      raspberryIp: faker.internet.ipv4({ cidrBlock: '***********/16' }),
      testPcIp: faker.internet.ipv4({ cidrBlock: '***********/16' }),
      testPcPower: faker.helpers.arrayElement([0, 1]),
      startTestTime: formatterCN.format(
        faker.date.between({ from: '2025-05-07', to: '2025-05-14' }),
      ),
      testScript: '',
      testItemName: '',
      user: faker.commerce.product(),
      progress: 0,
    };
    if (dataItem.status === 2) {
      dataItem.progress = faker.number.int({ min: 0, max: 100 });
      dataItem.testScript = faker.helpers.arrayElement([
        '企业级测试脚本.json',
        '消费级测试脚本.json',
        '工业级测试脚本.json',
        'BIT测试脚本.json',
        'FIO测试脚本.json',
      ]);
      dataItem.testItemName = faker.helpers.arrayElement([
        '掉电测试',
        '4K随机读测试',
        '4K随机写测试',
        '128K顺序读测试',
        '128K顺序写测试',
      ]);
    }

    dataList.push(dataItem);
  }

  return dataList;
}
const mockData = generateMockDataList(128);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }
  // const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
  // await delay(2000); // 延迟2秒
  const {
    page = 1,
    pageSize = 20,
    id,
    status,
    raspberryIp,
    testPcIp,
    user,
  } = getQuery(event);

  let listData = structuredClone(mockData);

  if (id) {
    listData = listData.filter((item) => item.id === Number(id));
  }
  if (status) {
    listData = listData.filter((item) => item.status === Number(status));
  }
  if (raspberryIp) {
    listData = listData.filter((item) => item.raspberryIp === raspberryIp);
  }
  if (testPcIp) {
    listData = listData.filter((item) => item.testPcIp === testPcIp);
  }
  if (user) {
    listData = listData.filter((item) =>
      item.user.toLowerCase().includes(String(user).toLowerCase()),
    );
  }
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
