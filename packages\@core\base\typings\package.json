{"name": "@vben-core/typings", "version": "5.5.5", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/@vben-core/base/typings"}, "license": "MIT", "type": "module", "scripts": {"build": "pnpm unbuild"}, "files": ["dist"], "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./src/index.ts", "development": "./src/index.ts", "default": "./dist/index.mjs"}, "./vue-router": {"types": "./vue-router.d.ts"}}, "publishConfig": {"exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}}}, "dependencies": {"vue": "catalog:", "vue-router": "catalog:"}}